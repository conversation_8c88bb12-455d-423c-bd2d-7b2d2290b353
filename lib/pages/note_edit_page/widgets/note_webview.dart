import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/note_edit_model.dart';

/// 笔记WebView组件
class NoteWebview extends StatefulWidget {
  /// 笔记数据模型
  final NoteEditModel noteModel;
  
  /// 页面开始加载回调
  final VoidCallback? onPageStarted;
  
  /// 页面加载完成回调
  final VoidCallback? onPageFinished;
  
  /// 页面加载错误回调
  final Function(String error)? onPageError;

  /// JavaScript执行回调
  final Function(String script)? onExecuteScript;

  const NoteWebview({
    super.key,
    required this.noteModel,
    this.onPageStarted,
    this.onPageFinished,
    this.onPageError,
    this.onExecuteScript,
  });

  @override
  State<NoteWebview> createState() => NoteWebviewState();
}

class NoteWebviewState extends State<NoteWebview> {
  late final WebViewController _webViewController;

  // JavaScript执行结果的Completer
  Completer<bool>? _jsExecutionCompleter;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  /// 执行JavaScript脚本
  Future<bool> executeScript(String script) async {
    try {
      print('准备执行JavaScript脚本: $script');

      // 创建新的Completer等待JavaScript执行结果
      _jsExecutionCompleter = Completer<bool>();

      // 生成唯一的执行ID，用于区分不同的执行请求
      final executionId = DateTime.now().millisecondsSinceEpoch.toString();

      // 简化的JavaScript包装脚本
      final wrappedScript = '''
try {
  $script;
  if (window.JSExecutionHelper) {
    window.JSExecutionHelper.reportResult(true, 'SUCCESS', '$executionId');
  }
} catch (error) {
  console.error('JavaScript执行错误:', error);
  if (window.JSExecutionHelper) {
    window.JSExecutionHelper.reportResult(false, error.name + ': ' + error.message, '$executionId');
  }
}
      ''';

      // 执行JavaScript
      await _webViewController.runJavaScript(wrappedScript);

      // 等待JavaScript执行结果回调，设置超时时间
      final result = await _jsExecutionCompleter!.future.timeout(
        Duration(seconds: 5),
        onTimeout: () {
          print('JavaScript执行超时，执行ID: $executionId');
          return false;
        },
      );

      print('JavaScript执行完成，执行ID: $executionId, 结果: $result');
      return result;
    } catch (e) {
      print('执行JavaScript失败: $e');
      return false;
    } finally {
      _jsExecutionCompleter = null;
    }
  }

  /// 获取当前页面的最新DOM结构
  Future<String?> getLatestDom() async {
    try {
      print('准备获取最新DOM结构');
      final result = await _webViewController.runJavaScriptReturningResult(
        'document.body.innerHTML'
      );

      if (result != null) {
        final domString = result.toString();
        print('成功获取DOM结构，长度: ${domString.length}');
        return domString;
      } else {
        print('获取DOM结构失败: 返回结果为空');
        return null;
      }
    } catch (e) {
      print('获取DOM结构失败: $e');
      return null;
    }
  }

  /// 加载HTML内容到WebView
  Future<void> loadHtmlContent(String htmlContent) async {
    try {
      print('开始加载HTML内容，长度: ${htmlContent.length}');

      // 解码HTML内容，处理转义字符
      String decodedHtml = htmlContent
          .replaceAll('\\n', '\n')
          .replaceAll('\\r', '\r')
          .replaceAll('\\t', '\t')
          .replaceAll('\\"', '"')
          .replaceAll("\\'", "'")
          .replaceAll('\\\\', '\\');

      print('HTML解码后长度: ${decodedHtml.length}');
      await _webViewController.loadHtmlString(decodedHtml);
      print('HTML内容加载完成');
    } catch (e) {
      print('加载HTML内容失败: $e');
    }
  }

  @override
  void didUpdateWidget(NoteWebview oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果笔记内容发生变化，重新加载
    if (oldWidget.noteModel.htmlContent != widget.noteModel.htmlContent) {
      _loadContent();
    }
  }

  /// 初始化WebView
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            widget.onPageStarted?.call();
          },
          onPageFinished: (String url) {
            // 页面加载完成后注入JavaScript Bridge
            _injectJavaScriptBridge();
            widget.onPageFinished?.call();
          },
          onWebResourceError: (WebResourceError error) {
            widget.onPageError?.call('加载失败: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterBridge',
        onMessageReceived: (JavaScriptMessage message) {
          _handleJavaScriptMessage(message);
        },
      );

    // 加载内容
    _loadContent();
  }

  /// 注入JavaScript Bridge
  void _injectJavaScriptBridge() {
final bridgeScript = '''
  window.JSExecutionHelper = {
    reportResult: function(success, message, executionId) {
      if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
        FlutterBridge.postMessage(JSON.stringify({
          type: 'jsExecutionResult',
          success: success,
          message: message,
          executionId: executionId || 'unknown'
        }));
      } else {
        console.error('FlutterBridge channel not available');
      }
    }
  };
  console.log('JSExecutionHelper注入成功');
''';

    _webViewController.runJavaScript(bridgeScript);
  }

  /// 处理JavaScript消息
  void _handleJavaScriptMessage(JavaScriptMessage message) {
    try {
      final data = jsonDecode(message.message) as Map<String, dynamic>;
      final type = data['type'] as String?;

      if (type == 'jsExecutionResult') {
        final success = data['success'] as bool? ?? false;
        final resultMessage = data['message'] as String? ?? '';
        final executionId = data['executionId'] as String? ?? 'unknown';

        print('收到JavaScript执行结果: success=$success, message=$resultMessage, executionId=$executionId');

        // 完成Completer
        if (_jsExecutionCompleter != null && !_jsExecutionCompleter!.isCompleted) {
          _jsExecutionCompleter!.complete(success);
        }
      }
    } catch (e) {
      print('处理JavaScript消息失败: $e');
      // 如果解析失败，认为执行失败
      if (_jsExecutionCompleter != null && !_jsExecutionCompleter!.isCompleted) {
        _jsExecutionCompleter!.complete(false);
      }
    }
  }

  /// 加载内容
  void _loadContent() {
    if (widget.noteModel.hasHtmlContent) {
      // 如果有HTML内容，直接加载
      _webViewController.loadHtmlString(widget.noteModel.htmlContent);
    } else {
      // 如果没有HTML内容，生成备用HTML
      final fallbackHtml = _buildFallbackHtml();
      _webViewController.loadHtmlString(fallbackHtml);
    }
  }

  /// 构建备用HTML内容
  String _buildFallbackHtml() {
    final model = widget.noteModel;
    
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${model.title}</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #fff;
                padding: 20px;
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
            }
            
            .title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                color: #1a1a1a;
            }
            
            .cover {
                width: 100%;
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            .desc {
                font-size: 16px;
                color: #666;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #1EB9EF;
            }
            
            .content {
                font-size: 16px;
                line-height: 1.8;
                white-space: pre-wrap;
            }
            
            .empty-content {
                text-align: center;
                color: #999;
                font-style: italic;
                padding: 40px 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="title">${model.title.isNotEmpty ? model.title : '无标题'}</div>
            ${model.hasCover ? '<img class="cover" src="${model.cover}" alt="封面图片" />' : ''}
            ${model.hasDescription ? '<div class="desc">${model.description}</div>' : ''}
            ${model.content.isNotEmpty ? '<div class="content">${model.content}</div>' : '<div class="empty-content">暂无内容</div>'}
        </div>
    </body>
    </html>
    ''';
  }

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(controller: _webViewController);
  }
}
